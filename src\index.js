const express = require("express");
const { createCanvas, loadImage, registerFont } = require("canvas");
const fs = require("fs");
const path = require("path");
const multer = require("multer");
const cors = require("cors");

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, "..", "public")));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Register the fonts if they exist
const fontPaths = {
  regular: path.join(__dirname, "..", "public", "NotoSans-Regular.ttf"),
  emoji: path.join(__dirname, "..", "public", "NotoEmoji-Regular.ttf"),
};

if (fileExists(fontPaths.regular)) {
  registerFont(fontPaths.regular, { family: "NotoSans" });
} else {
  console.error(`Font file not found: ${fontPaths.regular}`);
}

if (fileExists(fontPaths.emoji)) {
  registerFont(fontPaths.emoji, { family: "NotoEmoji" });
} else {
  console.error(`Font file not found: ${fontPaths.emoji}`);
}

app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  next();
});

function error(state, message, res) {
  if (state === "throw") {
    res.json({ Error: message });
  }
}

function getInfo(req, res) {
  const { name, icon, money, text } = req.query;
  if (name && icon && money && text) {
    return { name, icon, money, text };
  } else {
    error("throw", "Bad Request: Parameters are missing.", res);
    return null;
  }
}

function color(money) {
  money = parseInt(money, 10);
  if (money <= 19999) {
    return [
      "#1565C0",
      "#1565C0",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 49999) {
    return [
      "#00B8D4",
      "#00E5FF",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 99999) {
    return [
      "#00BFA5",
      "#1DE9B6",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 199999) {
    return [
      "#FFB300",
      "#FFCA28",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 499999) {
    return [
      "#E65100",
      "#F57C00",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 999999) {
    return [
      "#C2185B",
      "#E91E63",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else {
    return [
      "#D00000",
      "#E62117",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  }
}

async function wrapTextWithEmoji(ctx, text, x, y, maxWidth, lineHeight) {
  const words = text.split(" ");
  let line = "";
  let testLine = "";
  let testWidth = 0;
  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    testLine = line + word + " ";
    testWidth = ctx.measureText(testLine).width;

    if (testWidth > maxWidth && i > 0) {
      await drawTextWithEmoji(ctx, line, x, y);
      line = word + " ";
      y += lineHeight;
    } else {
      line = testLine;
    }
  }
  await drawTextWithEmoji(ctx, line, x, y);
  return y + lineHeight;
}

// Text sanitization function
function sanitizeText(text) {
  // Remove VARIATION SELECTOR-16 (U+FE0F)
  text = text.replace(/\uFE0F/g, "");

  // Normalize text to remove combining marks
  text = text.normalize("NFKD").replace(/[\u0300-\u036F]/g, "");

  // Remove zero-width and special Unicode characters
  text = text.replace(/[\u200B-\u200D\uFEFF]/g, "");

  return text;
}

// Simplified text rendering function (fallback to system emoji)
async function drawTextWithEmoji(ctx, text, x, y) {
  ctx.font = '12px "NotoSans", "NotoEmoji", "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji"';
  const sanitizedText = sanitizeText(text);

  // Use simple text rendering with system emoji fonts
  ctx.fillText(sanitizedText, x, y);

  return x + ctx.measureText(sanitizedText).width;
}

// Color preview endpoint
app.get("/api/color-preview", (req, res) => {
  const { money } = req.query;

  if (!money || isNaN(money)) {
    return res.status(400).json({
      success: false,
      error: "Valid money amount is required"
    });
  }

  const colors = color(money);
  res.json({
    success: true,
    data: {
      colors: colors,
      amount: money
    }
  });
});

// SuperChat preview endpoint (without file upload)
app.post("/api/preview", async (req, res) => {
  try {
    const { name, money, text, currency = 'IDR' } = req.body;

    if (!name || !money || !text) {
      return res.status(400).json({
        error: "Name, money, and text are required"
      });
    }

    const colors = color(money);
    const hexToRgb = (hex) => {
      const bigint = parseInt(hex.slice(1), 16);
      const r = (bigint >> 16) & 255;
      const g = (bigint >> 8) & 255;
      const b = bigint & 255;
      return [r, g, b];
    };
    const colorRgb = colors.map(hexToRgb);

    const width = 335;
    const maxWidth = 303;
    const lineHeight = 21;

    // Calculate text height
    const tempCanvas = createCanvas(width, 1000);
    const tempCtx = tempCanvas.getContext("2d");
    tempCtx.font = '12px "NotoSans", "NotoEmoji"';
    const textHeight = await wrapTextWithEmoji(
      tempCtx,
      text,
      0,
      0,
      maxWidth,
      lineHeight
    );
    const height = 56 + textHeight + 16;

    // Create main canvas
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext("2d");
    ctx.textDrawingMode = "glyph";

    // Draw background
    ctx.fillStyle = `rgb(${colorRgb[0][0]}, ${colorRgb[0][1]}, ${colorRgb[0][2]})`;
    ctx.fillRect(0, 0, width, 56);
    ctx.fillStyle = `rgb(${colorRgb[1][0]}, ${colorRgb[1][1]}, ${colorRgb[1][2]})`;
    ctx.fillRect(0, 56, width, height);

    // Draw username
    ctx.font = 'bold 14px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[2];
    await wrapTextWithEmoji(ctx, name, 72, 19, maxWidth, lineHeight);

    // Draw amount
    ctx.font = 'bold 16px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[4];
    const currencySymbol = currency === 'IDR' ? 'Rp' : '$';
    ctx.fillText(`${currencySymbol}${Number(money).toLocaleString("id-ID")}`, 72, 38);

    // Draw comment text
    ctx.font = '12px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[4];
    await wrapTextWithEmoji(ctx, text, 16, 77, maxWidth, lineHeight);

    // Draw placeholder icon (circle with user icon)
    ctx.fillStyle = '#9ca3af';
    ctx.beginPath();
    ctx.arc(36, 28, 20, 0, 2 * Math.PI);
    ctx.fill();

    // Draw user icon placeholder
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px "NotoSans"';
    ctx.textAlign = 'center';
    ctx.fillText('👤', 36, 33);
    ctx.textAlign = 'left';

    res.setHeader("Content-Type", "image/png");
    res.send(canvas.toBuffer());

  } catch (error) {
    console.error('Error generating SuperChat preview:', error);
    res.status(500).json({
      error: "Failed to generate SuperChat preview"
    });
  }
});

// Generate SuperChat with file upload
app.post("/api/generate", upload.single('icon'), async (req, res) => {
  try {
    const { name, money, text, currency = 'IDR', iconZoom = 1, iconOffsetX = 0, iconOffsetY = 0 } = req.body;

    if (!name || !money || !text) {
      return res.status(400).json({
        error: "Name, money, and text are required"
      });
    }

    if (!req.file) {
      return res.status(400).json({
        error: "Icon file is required"
      });
    }

    const colors = color(money);
    const hexToRgb = (hex) => {
      const bigint = parseInt(hex.slice(1), 16);
      const r = (bigint >> 16) & 255;
      const g = (bigint >> 8) & 255;
      const b = bigint & 255;
      return [r, g, b];
    };
    const colorRgb = colors.map(hexToRgb);

    const width = 335;
    const maxWidth = 303;
    const lineHeight = 21;

    // Calculate text height
    const tempCanvas = createCanvas(width, 1000);
    const tempCtx = tempCanvas.getContext("2d");
    tempCtx.font = '12px "NotoSans", "NotoEmoji"';
    const textHeight = await wrapTextWithEmoji(
      tempCtx,
      text,
      0,
      0,
      maxWidth,
      lineHeight
    );
    const height = 56 + textHeight + 16;

    // Create main canvas
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext("2d");
    ctx.textDrawingMode = "glyph";

    // Draw background
    ctx.fillStyle = `rgb(${colorRgb[0][0]}, ${colorRgb[0][1]}, ${colorRgb[0][2]})`;
    ctx.fillRect(0, 0, width, 56);
    ctx.fillStyle = `rgb(${colorRgb[1][0]}, ${colorRgb[1][1]}, ${colorRgb[1][2]})`;
    ctx.fillRect(0, 56, width, height);

    // Draw username
    ctx.font = 'bold 14px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[2];
    await wrapTextWithEmoji(ctx, name, 72, 19, maxWidth, lineHeight);

    // Draw amount
    ctx.font = 'bold 16px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[4];
    const currencySymbol = currency === 'IDR' ? 'Rp' : '$';
    ctx.fillText(`${currencySymbol}${Number(money).toLocaleString("id-ID")}`, 72, 38);

    // Draw comment text
    ctx.font = '12px "NotoSans", "NotoEmoji"';
    ctx.fillStyle = colors[4];
    await wrapTextWithEmoji(ctx, text, 16, 77, maxWidth, lineHeight);

    // Load and draw icon
    const iconImage = await loadImage(req.file.buffer);

    // Apply icon transformations
    const iconSize = 40;
    const iconX = 16 + parseFloat(iconOffsetX);
    const iconY = 8 + parseFloat(iconOffsetY);
    const zoom = parseFloat(iconZoom);
    const scaledSize = iconSize * zoom;

    ctx.drawImage(iconImage, iconX, iconY, scaledSize, scaledSize);

    res.setHeader("Content-Type", "image/png");
    res.send(canvas.toBuffer());

  } catch (error) {
    console.error('Error generating SuperChat:', error);
    res.status(500).json({
      error: "Failed to generate SuperChat image"
    });
  }
});

// Keep the original endpoint for backward compatibility
app.get("/generate-image", async (req, res) => {
  const info = getInfo(req, res);
  if (!info) return;

  const { name, icon, money, text } = info;
  const colors = color(money);
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return [r, g, b];
  };
  const colorRgb = colors.map(hexToRgb);

  const iconUrl = `https://api.pocopota.com/icon-maker?size=40&url=${icon}`;
  const width = 335;
  const maxWidth = 303;
  const lineHeight = 21;

  const tempCanvas = createCanvas(width, 1000);
  const tempCtx = tempCanvas.getContext("2d");
  tempCtx.font = '12px "NotoSans", "NotoEmoji"';
  const textHeight = await wrapTextWithEmoji(
    tempCtx,
    text,
    0,
    0,
    maxWidth,
    lineHeight
  );
  const height = 56 + textHeight + 16;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");
  ctx.textDrawingMode = "glyph";
  ctx.fillStyle = `rgb(${colorRgb[0][0]}, ${colorRgb[0][1]}, ${colorRgb[0][2]})`;
  ctx.fillRect(0, 0, width, 56);
  ctx.fillStyle = `rgb(${colorRgb[1][0]}, ${colorRgb[1][1]}, ${colorRgb[1][2]})`;
  ctx.fillRect(0, 56, width, height);

  ctx.font = 'bold 14px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[2];
  await wrapTextWithEmoji(ctx, name, 72, 19, maxWidth, lineHeight);

  ctx.font = 'bold 16px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[4];
  ctx.fillText(`Rp${Number(money).toLocaleString("id-ID")}`, 72, 38);

  ctx.font = '12px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[4];
  await wrapTextWithEmoji(ctx, text, 16, 77, maxWidth, lineHeight);

  loadImage(iconUrl)
    .then((iconImage) => {
      ctx.drawImage(iconImage, 16, 8, 40, 40);
      res.setHeader("Content-Type", "image/png");
      res.send(canvas.toBuffer());
    })
    .catch(() => {
      error("throw", "Failed to load icon image.", res);
    });
});

// Start server if not being imported as module
if (require.main === module) {
  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    console.log(`SuperChat Generator API running on port ${PORT}`);
    console.log(`Visit http://localhost:${PORT} to use the web interface`);
  });
}

module.exports = app;
