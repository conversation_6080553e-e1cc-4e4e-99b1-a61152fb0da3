/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Layout */
.main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

@media (max-width: 768px) {
    .main {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

/* Form Section */
.form-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.label {
    font-weight: 500;
    color: #374151;
    font-size: 0.95rem;
}

.input-wrapper {
    position: relative;
}

.input, .textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    font-family: inherit;
}

.input:focus, .textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea {
    resize: vertical;
    min-height: 80px;
}

.char-counter {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 0.8rem;
    color: #6b7280;
    background: white;
    padding: 2px 4px;
    border-radius: 4px;
}

/* Amount Input */
.amount-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.currency-wrapper {
    display: flex;
    gap: 8px;
}

.currency-select {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    min-width: 120px;
}

.amount-input {
    flex: 1;
}

.color-preview {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.color-bar {
    width: 60px;
    height: 20px;
    border-radius: 10px;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
}

.color-text {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.file-upload-area:hover, .file-upload-area.dragover {
    border-color: #667eea;
    background: #f8faff;
}

.file-input {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: pointer;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 12px;
}

.upload-text {
    margin-bottom: 4px;
}

.upload-primary {
    color: #667eea;
    font-weight: 500;
}

.upload-secondary {
    font-size: 0.9rem;
    color: #6b7280;
}

/* File Preview */
.file-preview {
    position: relative;
    display: inline-block;
    margin-top: 12px;
}

.preview-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

.remove-file-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Icon Editor */
.icon-editor {
    background: #f9fafb;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.editor-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: #374151;
}

.editor-content {
    display: flex;
    gap: 20px;
    align-items: center;
}

.icon-preview-container {
    flex-shrink: 0;
}

.icon-preview-frame {
    width: 60px;
    height: 60px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    cursor: move;
}

.icon-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.icon-drag-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.icon-preview-frame:hover .icon-drag-overlay {
    opacity: 1;
}

.editor-controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.control-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
}

.zoom-slider {
    width: 100%;
}

.editor-buttons {
    display: flex;
    gap: 8px;
}

.editor-btn {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s;
}

.editor-btn:hover {
    background: #f3f4f6;
}

/* Generate Button */
.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: flex;
    align-items: center;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Preview Section */
.preview-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: fit-content;
}

.preview-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #374151;
}

.preview-area {
    min-height: 200px;
    border: 2px dashed #e5e7eb;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.preview-placeholder {
    text-align: center;
    color: #9ca3af;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 12px;
}

.preview-image {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.live-preview-container,
.final-preview-container {
    position: relative;
    display: inline-block;
}

.preview-label {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-label.final {
    background: #10b981;
}

.preview-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.action-btn {
    padding: 10px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.download-btn:hover {
    border-color: #10b981;
    color: #10b981;
}

.copy-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

/* Toast */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #10b981;
}

.toast.error {
    background: #ef4444;
}
