<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperChat Generator</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">SuperChat Generator</h1>
            <p class="subtitle">Create YouTube-style SuperChat messages with custom icons and amounts</p>
        </header>

        <main class="main">
            <div class="form-section">
                <form id="superchatForm" class="form">
                    <!-- Username Input -->
                    <div class="input-group">
                        <label for="username" class="label">Username</label>
                        <div class="input-wrapper">
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="input" 
                                placeholder="Enter username"
                                maxlength="50"
                                required
                            >
                            <span class="char-counter">
                                <span id="usernameCount">0</span>/50
                            </span>
                        </div>
                    </div>

                    <!-- Amount Input -->
                    <div class="input-group">
                        <label for="amount" class="label">Amount</label>
                        <div class="amount-wrapper">
                            <div class="currency-wrapper">
                                <select id="currency" name="currency" class="currency-select">
                                    <option value="IDR">IDR (Rp)</option>
                                    <option value="USD">USD ($)</option>
                                </select>
                                <input 
                                    type="number" 
                                    id="amount" 
                                    name="amount" 
                                    class="input amount-input" 
                                    placeholder="0"
                                    min="1"
                                    max="10000000"
                                    required
                                >
                            </div>
                            <div class="color-preview">
                                <div id="colorBar" class="color-bar"></div>
                                <span id="colorText" class="color-text">Enter amount to see color</span>
                            </div>
                        </div>
                    </div>

                    <!-- Comment Input -->
                    <div class="input-group">
                        <label for="comment" class="label">Comment</label>
                        <div class="input-wrapper">
                            <textarea 
                                id="comment" 
                                name="comment" 
                                class="textarea" 
                                placeholder="Enter your SuperChat message"
                                maxlength="200"
                                rows="3"
                                required
                            ></textarea>
                            <span class="char-counter">
                                <span id="commentCount">0</span>/200
                            </span>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div class="input-group">
                        <label class="label">Profile Icon</label>
                        <div id="fileUploadArea" class="file-upload-area">
                            <input type="file" id="iconFile" name="icon" accept="image/*" class="file-input" required>
                            <div class="upload-content">
                                <div class="upload-icon">📁</div>
                                <p class="upload-text">
                                    <span class="upload-primary">Click to upload</span> or drag and drop
                                </p>
                                <p class="upload-secondary">PNG, JPG, GIF up to 5MB</p>
                            </div>
                        </div>
                        
                        <!-- File Preview -->
                        <div id="filePreview" class="file-preview" style="display: none;">
                            <img id="previewImage" class="preview-image" alt="Preview">
                            <button type="button" id="removeFile" class="remove-file-btn">×</button>
                        </div>
                    </div>

                    <!-- Icon Editor -->
                    <div id="iconEditor" class="icon-editor" style="display: none;">
                        <h3 class="editor-title">Icon Editor</h3>
                        <div class="editor-content">
                            <div class="icon-preview-container">
                                <div class="icon-preview-frame">
                                    <img id="iconPreviewImage" class="icon-preview-image" alt="Icon Preview">
                                    <div id="iconDragOverlay" class="icon-drag-overlay">
                                        <span>Drag to reposition</span>
                                    </div>
                                </div>
                            </div>
                            <div class="editor-controls">
                                <div class="control-group">
                                    <label for="iconZoom" class="control-label">Zoom: <span id="zoomValue">1.0x</span></label>
                                    <input type="range" id="iconZoom" min="0.5" max="3" step="0.1" value="1" class="zoom-slider">
                                </div>
                                <div class="editor-buttons">
                                    <button type="button" id="centerIcon" class="editor-btn">Center</button>
                                    <button type="button" id="resetIcon" class="editor-btn">Reset</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <button type="submit" id="generateBtn" class="generate-btn">
                        <span class="btn-text">Generate SuperChat</span>
                        <div id="loadingSpinner" class="loading-spinner" style="display: none;">
                            <div class="spinner"></div>
                        </div>
                    </button>
                </form>
            </div>

            <!-- Preview Section -->
            <div class="preview-section">
                <div class="preview-container">
                    <h3 class="preview-title">Preview</h3>
                    <div id="previewArea" class="preview-area">
                        <div class="preview-placeholder">
                            <div class="placeholder-icon">🖼️</div>
                            <p>Your SuperChat will appear here</p>
                        </div>
                    </div>
                    <div id="previewActions" class="preview-actions" style="display: none;">
                        <button id="downloadBtn" class="action-btn download-btn">
                            <span>📥</span> Download
                        </button>
                        <button id="copyBtn" class="action-btn copy-btn">
                            <span>📋</span> Copy
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>

    <script src="script.js"></script>
</body>
</html>
