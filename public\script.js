class SuperChatGenerator {
    constructor() {
        this.form = document.getElementById('superchatForm');
        this.fileInput = document.getElementById('iconFile');
        this.fileUploadArea = document.getElementById('fileUploadArea');
        this.filePreview = document.getElementById('filePreview');
        this.previewImage = document.getElementById('previewImage');
        this.removeFileBtn = document.getElementById('removeFile');
        this.generateBtn = document.getElementById('generateBtn');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.previewArea = document.getElementById('previewArea');
        this.previewActions = document.getElementById('previewActions');
        this.downloadBtn = document.getElementById('downloadBtn');
        this.copyBtn = document.getElementById('copyBtn');
        this.toast = document.getElementById('toast');

        // Icon editor elements
        this.iconEditor = document.getElementById('iconEditor');
        this.iconPreviewImage = document.getElementById('iconPreviewImage');
        this.iconDragOverlay = document.getElementById('iconDragOverlay');
        this.iconZoomSlider = document.getElementById('iconZoom');
        this.zoomValue = document.getElementById('zoomValue');
        this.centerIconBtn = document.getElementById('centerIcon');
        this.resetIconBtn = document.getElementById('resetIcon');

        this.currentImageBlob = null;
        this.iconSettings = {
            zoom: 1,
            offsetX: 0,
            offsetY: 0
        };
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };

        this.initializeEventListeners();
        this.initializeCharacterCounters();
        this.initializeFileUpload();
        this.initializeAmountPreview();
        this.initializeIconEditor();
    }

    initializeEventListeners() {
        this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        this.removeFileBtn.addEventListener('click', this.removeFile.bind(this));
        this.downloadBtn.addEventListener('click', this.downloadImage.bind(this));
        this.copyBtn.addEventListener('click', this.copyToClipboard.bind(this));
    }

    initializeIconEditor() {
        // Zoom slider
        this.iconZoomSlider.addEventListener('input', (e) => {
            this.iconSettings.zoom = parseFloat(e.target.value);
            this.zoomValue.textContent = `${this.iconSettings.zoom.toFixed(1)}x`;
            this.updateIconPreview();
        });

        // Center button
        this.centerIconBtn.addEventListener('click', () => {
            this.iconSettings.offsetX = 0;
            this.iconSettings.offsetY = 0;
            this.updateIconPreview();
        });

        // Reset button
        this.resetIconBtn.addEventListener('click', () => {
            this.iconSettings = { zoom: 1, offsetX: 0, offsetY: 0 };
            this.iconZoomSlider.value = 1;
            this.zoomValue.textContent = '1.0x';
            this.updateIconPreview();
        });

        // Drag functionality
        this.iconPreviewImage.addEventListener('mousedown', this.startDrag.bind(this));
        this.iconPreviewImage.addEventListener('touchstart', this.startDrag.bind(this));

        document.addEventListener('mousemove', this.handleDrag.bind(this));
        document.addEventListener('touchmove', this.handleDrag.bind(this));

        document.addEventListener('mouseup', this.endDrag.bind(this));
        document.addEventListener('touchend', this.endDrag.bind(this));
    }

    startDrag(e) {
        e.preventDefault();
        this.isDragging = true;

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        this.dragStart = {
            x: clientX,
            y: clientY,
            offsetX: this.iconSettings.offsetX,
            offsetY: this.iconSettings.offsetY
        };
    }

    handleDrag(e) {
        if (!this.isDragging) return;
        e.preventDefault();

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        const deltaX = clientX - this.dragStart.x;
        const deltaY = clientY - this.dragStart.y;

        this.iconSettings.offsetX = this.dragStart.offsetX + deltaX * 0.5;
        this.iconSettings.offsetY = this.dragStart.offsetY + deltaY * 0.5;

        // Limit the offset range
        this.iconSettings.offsetX = Math.max(-20, Math.min(20, this.iconSettings.offsetX));
        this.iconSettings.offsetY = Math.max(-20, Math.min(20, this.iconSettings.offsetY));

        this.updateIconPreview();
    }

    endDrag() {
        this.isDragging = false;
    }

    updateIconPreview() {
        if (!this.iconPreviewImage.src) return;

        const zoom = this.iconSettings.zoom;
        const offsetX = this.iconSettings.offsetX;
        const offsetY = this.iconSettings.offsetY;

        this.iconPreviewImage.style.transform =
            `scale(${zoom}) translate(${offsetX}px, ${offsetY}px)`;
    }

    initializeCharacterCounters() {
        const usernameInput = document.getElementById('username');
        const commentInput = document.getElementById('comment');
        const usernameCount = document.getElementById('usernameCount');
        const commentCount = document.getElementById('commentCount');

        usernameInput.addEventListener('input', () => {
            usernameCount.textContent = usernameInput.value.length;
        });

        commentInput.addEventListener('input', () => {
            commentCount.textContent = commentInput.value.length;
        });
    }

    initializeFileUpload() {
        this.fileUploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.fileUploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.fileUploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    initializeAmountPreview() {
        const amountInput = document.getElementById('amount');
        amountInput.addEventListener('input', this.updateColorPreview.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        this.fileUploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.fileUploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.fileUploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.fileInput.files = files;
            this.handleFileSelect();
        }
    }

    handleFileSelect() {
        const file = this.fileInput.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showToast('Please select an image file', 'error');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            this.showToast('File size must be less than 5MB', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.previewImage.src = e.target.result;
            this.iconPreviewImage.src = e.target.result;
            this.filePreview.style.display = 'block';
            this.iconEditor.style.display = 'block';
            this.fileUploadArea.style.display = 'none';
            this.updateIconPreview();
        };
        reader.readAsDataURL(file);
    }

    removeFile() {
        this.fileInput.value = '';
        this.filePreview.style.display = 'none';
        this.iconEditor.style.display = 'none';
        this.fileUploadArea.style.display = 'block';
        this.previewImage.src = '';
        this.iconPreviewImage.src = '';

        // Reset icon settings
        this.iconSettings = { zoom: 1, offsetX: 0, offsetY: 0 };
        this.iconZoomSlider.value = 1;
        this.zoomValue.textContent = '1.0x';
    }

    async updateColorPreview() {
        const amount = document.getElementById('amount').value;
        const colorBar = document.getElementById('colorBar');
        const colorText = document.getElementById('colorText');

        if (!amount || amount <= 0) {
            colorBar.style.background = '#f3f4f6';
            colorText.textContent = 'Enter amount to see color';
            return;
        }

        try {
            const response = await fetch(`/api/color-preview?money=${amount}`);
            const data = await response.json();

            if (data.success) {
                const colors = data.data.colors;
                colorBar.style.background = `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`;

                const tier = this.getAmountTier(amount);
                colorText.textContent = `${tier} tier`;
            }
        } catch (error) {
            console.error('Error fetching color preview:', error);
        }
    }

    getAmountTier(amount) {
        amount = Number(amount);
        if (amount <= 19999) return 'Blue';
        if (amount <= 49999) return 'Cyan';
        if (amount <= 99999) return 'Green';
        if (amount <= 199999) return 'Yellow';
        if (amount <= 499999) return 'Orange';
        if (amount <= 999999) return 'Magenta';
        return 'Red';
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        if (!this.validateForm()) {
            return;
        }

        this.setLoading(true);

        try {
            const formData = new FormData();
            formData.append('name', document.getElementById('username').value.trim());
            formData.append('money', document.getElementById('amount').value);
            formData.append('text', document.getElementById('comment').value.trim());
            formData.append('currency', document.getElementById('currency').value);
            formData.append('icon', this.fileInput.files[0]);

            // Add icon positioning data
            formData.append('iconZoom', this.iconSettings.zoom.toString());
            formData.append('iconOffsetX', this.iconSettings.offsetX.toString());
            formData.append('iconOffsetY', this.iconSettings.offsetY.toString());

            const response = await fetch('/api/generate', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const blob = await response.blob();
                this.currentImageBlob = blob;
                this.displayPreview(blob);
                this.showToast('SuperChat generated successfully!');
            } else {
                const errorData = await response.json();
                this.showToast(errorData.error || 'Failed to generate SuperChat', 'error');
            }
        } catch (error) {
            console.error('Error generating SuperChat:', error);
            this.showToast('Network error. Please try again.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    validateForm() {
        const username = document.getElementById('username').value.trim();
        const amount = document.getElementById('amount').value;
        const comment = document.getElementById('comment').value.trim();
        const file = this.fileInput.files[0];

        if (!username) {
            this.showToast('Username is required', 'error');
            return false;
        }

        if (!amount || amount <= 0) {
            this.showToast('Valid amount is required', 'error');
            return false;
        }

        if (!comment) {
            this.showToast('Comment is required', 'error');
            return false;
        }

        if (!file) {
            this.showToast('Profile icon is required', 'error');
            return false;
        }

        return true;
    }

    setLoading(loading) {
        this.generateBtn.disabled = loading;
        this.loadingSpinner.style.display = loading ? 'block' : 'none';
        document.querySelector('.btn-text').textContent = loading ? 'Generating...' : 'Generate SuperChat';
    }

    displayPreview(blob) {
        const url = URL.createObjectURL(blob);
        this.previewArea.innerHTML = `<img src="${url}" alt="Generated SuperChat" class="preview-image">`;
        this.previewActions.style.display = 'flex';
    }

    downloadImage() {
        if (!this.currentImageBlob) return;

        const url = URL.createObjectURL(this.currentImageBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `superchat-${Date.now()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('Image downloaded successfully!');
    }

    async copyToClipboard() {
        if (!this.currentImageBlob) return;

        try {
            await navigator.clipboard.write([
                new ClipboardItem({
                    'image/png': this.currentImageBlob
                })
            ]);
            this.showToast('Image copied to clipboard!');
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            this.showToast('Failed to copy to clipboard', 'error');
        }
    }

    showToast(message, type = 'success') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');

        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SuperChatGenerator();
});